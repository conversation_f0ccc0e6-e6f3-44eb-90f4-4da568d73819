import {useContext, useEffect, useState} from "react"
import {But<PERSON>} from "@/components/ui/button"
import {Label} from "@/components/ui/label"
import {RadioGroup, RadioGroupItem} from "@/components/ui/radio-group"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {AlertTriangle, Filter, X} from "lucide-react"
import {CoverageContext} from "@/contexts/CoverageContext"
import {usePathname, useRouter, useSearchParams} from "next/navigation"
import {<PERSON>} from "@/types/doctor"
import {doctors as staticDoctors} from "@/data/doctors"
import {initialMedicalCenters as staticMedicalCenters} from "@/data/medicalCenters"

interface SearchResultsSidebarProps {
    open: boolean
    onClose: () => void
    noInsurance: boolean
    setNoInsurance: (value: boolean) => void
    onApplyFilters?: (coverageData: {
        noInsurance: boolean,
        coverageId: string,
        coverageName: string,
        plan: string
    }) => void
    onTimeFilterChange?: (value: string) => void
    onSortChange?: (value: "date" | "distance") => void
    sortBy?: "date" | "distance"
    searchType?: string
    searchQuery?: string
    locationQuery?: string
    renderMode?: "drawer" | "popover"
    selectedTime?: string
}

export default function SearchResultsSidebar({
                                                 open,
                                                 onClose,
                                                 noInsurance,
                                                 setNoInsurance,
                                                 onApplyFilters,
                                                 onTimeFilterChange,
                                                 onSortChange,
                                                 sortBy = "date",
                                                 searchType = "",
                                                 searchQuery = "",
                                                  locationQuery = "",
                                                  renderMode = "drawer",
                                                  selectedTime
                                             }: SearchResultsSidebarProps) {
    const {medicalCoverages, isDoctorCoverageExcluded} = useContext(CoverageContext)
    const router = useRouter()
    const pathname = usePathname()
    const searchParams = useSearchParams()

    // Update a simple filter parameter and apply filters
    const updateSimpleFilterParam = (name: string, value: string) => {
        const params = new URLSearchParams(searchParams.toString());
        params.set(name, value);
        router.replace(`${pathname}?${params.toString()}`);

        if (name === "time" && onTimeFilterChange) {
            onTimeFilterChange(value);
        }
    }

    const timeValue = selectedTime ?? (searchParams.get("time") || "all");

    return (
        <div
            className={
                renderMode === "popover"
                    ? "w-full md:w-[56rem] bg-white rounded-lg border border-gray-200 shadow-lg overflow-y-auto max-h-[80vh]"
                    : `fixed inset-y-0 left-0 transform ${open ? "translate-x-0" : "-translate-x-full"} w-64 bg-white shadow-lg transition-transform duration-300 ease-in-out z-[80] md:z-20 md:sticky md:top-20 md:translate-x-0 md:h-fit rounded-xl md:rounded-lg border md:border-gray-200 border-[#0070F3]/30 overflow-y-auto`
            }>
            <div className="p-4 md:p-5">
                <div className="flex justify-between items-center mb-6 md:hidden">
                    <div className="flex items-center">
                        <Filter className="w-5 h-5 mr-2 text-[#0070F3]"/>
                        <h2 className="text-lg text-[#1c2533] font-semibold font-sans">Filtros</h2>
                    </div>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={onClose}
                        className="hover:bg-[#0070F3]/5 hover:text-[#0070F3]"
                    >
                        <X className="w-5 h-5"/>
                    </Button>
                </div>

                <div className="space-y-4 md:space-y-5">
                    <div>
                        <Label htmlFor="sort" className="text-sm font-medium text-gray-700 mb-1 block">Ordenar por</Label>
                        <Select
                            value={sortBy}
                            onValueChange={(value: "date" | "distance") => {
                                updateSimpleFilterParam("sort", value);
                                if (onSortChange) {
                                    onSortChange(value);
                                }
                            }}
                        >
                            <SelectTrigger id="sort"
                                           className="w-full h-10 text-sm bg-white border-gray-200 focus:ring-[#0070F3] focus:border-[#0070F3]">
                                <SelectValue placeholder="Seleccionar orden"/>
                            </SelectTrigger>
                            <SelectContent className="z-[90]">
                                <SelectItem value="date">Primer turno disponible</SelectItem>
                                <SelectItem value="distance">Menor distancia</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div>
                        <Label htmlFor="time" className="text-sm font-medium text-gray-700 mb-1 block">Horario</Label>
                        <Select
                            value={timeValue}
                            onValueChange={(value) => updateSimpleFilterParam("time", value)}
                        >
                            <SelectTrigger id="time"
                                           className="w-full h-10 text-sm bg-white border-gray-200 focus:ring-[#0070F3] focus:border-[#0070F3]">
                                <SelectValue placeholder="Seleccionar horario"/>
                            </SelectTrigger>
                            <SelectContent className="z-[90]">
                                <SelectItem value="all">Todos</SelectItem>
                                <SelectItem value="morning">Mañana (hasta 12:00)</SelectItem>
                                <SelectItem value="afternoon">Tarde (12:00 a 17:00)</SelectItem>
                                <SelectItem value="evening">Noche (desde 17:00)</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    {/* Coverage UI removed; handled by UnifiedSearch */}
                </div>
            </div>
        </div>
    )
}

