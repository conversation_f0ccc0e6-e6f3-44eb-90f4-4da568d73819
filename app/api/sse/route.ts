import {NextRequest} from 'next/server';

export async function GET(request: NextRequest) {
    try {
        // Extract query parameters
        const {searchParams} = new URL(request.url);
        const medicalCenterId = searchParams.get('medical-center-id');
        const employeeUserId = searchParams.get('employee-user-id');

        if (!medicalCenterId || !employeeUserId) {
            return new Response(JSON.stringify({error: 'Missing required parameters'}), {
                status: 400,
                headers: {'Content-Type': 'application/json'},
            });
        }

        console.log(`Connecting to SSE for Medical Center: ${medicalCenterId}, Employee: ${employeeUserId}`);
        console.log('Backend URL:', process.env.BACKEND_URL);

        // Create AbortController for the backend request
        const backendAbortController = new AbortController();

        // Use fetch to connect to the external SSE endpoint with parameters
        const response = await fetch(
            `${process.env.BACKEND_URL}/sse/data/${medicalCenterId}?employee-user-id=${employeeUserId}`,
            {
                headers: {
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                },
                signal: backendAbortController.signal,
            }
        );

        if (!response.ok) {
            console.error(`Backend SSE failed: ${response.status} ${response.statusText}`);
            throw new Error(`Failed to connect to backend stream: ${response.status} ${response.statusText}`);
        }

        if (!response.body) {
            throw new Error('No response body from backend');
        }

        console.log('Successfully connected to backend SSE');

        // Create a readable stream that will proxy the SSE connection
        const stream = new ReadableStream({
            start(controller) {
                const reader = response.body!.getReader();
                const decoder = new TextDecoder();
                let isStreamClosed = false;

                let closeStream = () => {
                    if (!isStreamClosed) {
                        isStreamClosed = true;
                        try {
                            reader.cancel();
                            backendAbortController.abort();
                            controller.close();
                        } catch (error) {
                            console.error('Error closing stream:', error);
                        }
                    }
                };

                const pump = async () => {
                    try {
                        // Send initial connection confirmation
                        const initialData = `data: ${JSON.stringify({
                            type: 'connection',
                            status: 'connected',
                            timestamp: new Date().toISOString()
                        })}\n\n`;
                        controller.enqueue(new TextEncoder().encode(initialData));

                        while (!isStreamClosed) {
                            const {done, value} = await reader.read();

                            if (done) {
                                console.log('Backend stream ended');
                                break;
                            }

                            // Decode the chunk and forward it to the client
                            const chunk = decoder.decode(value, {stream: true});

                            // Log received data for debugging
                            if (chunk.trim()) {
                                console.log('Received chunk from backend:', chunk.substring(0, 200));
                            }

                            controller.enqueue(new TextEncoder().encode(chunk));
                        }
                    } catch (error) {
                        console.error('Stream pump error:', error);

                        // Send error event to client
                        if (!isStreamClosed) {
                            try {
                                const errorData = `data: ${JSON.stringify({
                                    type: 'error',
                                    error: 'Backend connection lost',
                                    timestamp: new Date().toISOString()
                                })}\n\n`;
                                controller.enqueue(new TextEncoder().encode(errorData));
                            } catch (e) {
                                console.error('Error sending error event:', e);
                            }
                        }

                        controller.error(error);
                    } finally {
                        closeStream();
                    }
                };

                // Start the pump
                pump();

                // Clean up when the client disconnects
                request.signal.addEventListener('abort', () => {
                    console.log('Client disconnected, cleaning up SSE connection');
                    closeStream();
                });

                // Add a timeout to prevent zombie connections
                const timeout = setTimeout(() => {
                    console.log('SSE connection timeout, closing');
                    closeStream();
                }, 24 * 60 * 60 * 1000); // 24 hours

                // Clear timeout on cleanup
                const originalClose = closeStream;
                const closeStreamWithTimeout = () => {
                    clearTimeout(timeout);
                    originalClose();
                };

                // Replace the closeStream function
                closeStream = closeStreamWithTimeout;
            }
        });

        return new Response(stream, {
            headers: {
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Cache-Control',
                'X-Accel-Buffering': 'no', // Disable nginx buffering
            },
        });
    } catch (error) {
        console.error('API route error:', error);
        return new Response(
            JSON.stringify({
                error: 'Failed to connect to stream',
                details: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date().toISOString()
            }),
            {
                status: 500,
                headers: {'Content-Type': 'application/json'},
            }
        );
    }
}

export async function OPTIONS() {
    return new Response(null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Cache-Control',
            'Access-Control-Max-Age': '86400',
        },
    });
}